import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { AuthuserService } from 'src/app/services/authuser.service';
import { ThemeService } from 'src/app/services/theme.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  isDarkMode$: Observable<boolean>;
  dashboardStats: any = null;
  loading = true;
  error: string | null = null;

  recentProjects = [
    {
      name: 'E-Commerce Platform',
      category: 'WEB DESIGN',
      description: 'Create a user-friendly e-commerce platform with a sleek design and intuitive navigation.',
      team: [
        { name: 'Team member 1', avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },
        { name: 'Team member 2', avatar: 'https://randomuser.me/api/portraits/men/32.jpg' }
      ],
      dueDate: 'Due in 3 days'
    },
    // ... autres projets
  ];

  testimonials = [
    {
      name: '<PERSON><PERSON>',
      position: 'Project Manager',
      quote: '<PERSON><PERSON><PERSON> has transformed how our team collaborates...'
    },
    // ... autres témoignages
  ];

  constructor(
    public authService: AuthuserService,
    private themeService: ThemeService
  ) {
    this.isDarkMode$ = this.themeService.darkMode$;
  }

  ngOnInit(): void {
    if (this.authService.userLoggedIn()) {
      this.loadDashboardStats();
    } else {
      this.loading = false;
    }
  }

  loadDashboardStats(): void {
    this.loading = true;
    this.error = null;

    this.authService.getDashboardStats().subscribe({
      next: (stats) => {
        this.dashboardStats = stats;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard stats:', error);
        this.error = 'Failed to load dashboard statistics';
        this.loading = false;
      }
    });
  }

  toggleDarkMode(): void {
    this.themeService.toggleDarkMode();
  }

  // Ajoutez cette méthode pour vérifier si l'utilisateur est admin
  isAdmin(): boolean {
    const user = this.authService.getCurrentUser();
    return user && user.role === 'admin'; // Adaptez selon votre structure de données
  }
}
