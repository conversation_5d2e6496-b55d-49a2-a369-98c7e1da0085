<!-- Clean Modern Design -->
<div class="min-h-screen bg-gray-50">
  <!-- Top Navigation -->
  <nav class="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <h1 class="text-2xl font-bold text-gray-900">DevBridge</h1>
        </div>
        <div class="flex items-center space-x-4">
          <ng-container *ngIf="!authService.userLoggedIn(); else userActions">
            <a routerLink="/login" class="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors">Sign In</a>
            <a routerLink="/registeruser" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">Get Started</a>
          </ng-container>
          <ng-template #userActions>
            <span class="text-gray-700 text-sm font-medium">{{ authService.getCurrentUser()?.username }}</span>
            <button (click)="refreshDashboard()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors" title="Refresh">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>
          </ng-template>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

    <!-- Hero Section for Non-Logged Users -->
    <div *ngIf="!authService.userLoggedIn()" class="text-center py-20">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Effortless Task Management
          <span class="block text-blue-600">for Modern Teams</span>
        </h1>
        <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Streamline your workflow, collaborate seamlessly, and achieve more with our intuitive project management platform.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a routerLink="/registeruser" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
            Get Started Free
          </a>
          <a routerLink="/login" class="border border-gray-300 hover:border-gray-400 text-gray-700 px-8 py-3 rounded-lg font-semibold transition-colors">
            Sign In
          </a>
        </div>
      </div>
    </div>

    <!-- Dashboard Section for Logged Users -->
    <div *ngIf="authService.userLoggedIn()" class="py-8">

      <!-- Loading State -->
      <div *ngIf="loading" class="text-center py-20">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">Loading your dashboard...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="!loading && error" class="text-center py-20">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <svg class="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <h3 class="text-lg font-semibold text-red-800 mb-2">Unable to Load Dashboard</h3>
          <p class="text-red-600 mb-4">{{ error }}</p>
          <button (click)="refreshDashboard()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Try Again
          </button>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div *ngIf="!loading && !error && dashboardStats">

        <!-- Welcome Header -->
        <div class="mb-8">
          <h1 class="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {{ authService.getCurrentUser()?.username }}!
            <span *ngIf="isAdmin()" class="text-lg text-blue-600 font-normal">(Administrator)</span>
          </h1>
          <p class="text-gray-600">
            <ng-container *ngIf="dashboardStats.userRole === 'admin'; else regularUserSummary">
              You're managing {{ dashboardStats.totalUsers }} users across {{ dashboardStats.totalProjects }} projects with {{ dashboardStats.pendingTasks }} pending tasks.
            </ng-container>
            <ng-template #regularUserSummary>
              You have {{ dashboardStats.activeProjects }} active projects with {{ dashboardStats.pendingTasks }} pending tasks.
            </ng-template>
          </p>
        </div>

        <!-- Quick Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">

          <!-- Admin Stats -->
          <ng-container *ngIf="dashboardStats.userRole === 'admin'">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-lg">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Total Users</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.totalUsers }}</p>
                  <p class="text-sm text-green-600">{{ dashboardStats.activeUsers }} active</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-lg">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Total Projects</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.totalProjects }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-orange-100 rounded-lg">
                  <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Pending Tasks</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.pendingTasks }}</p>
                  <p class="text-sm text-green-600">{{ dashboardStats.completedTasks }} completed</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-purple-100 rounded-lg">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Total Teams</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.totalTeams }}</p>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- User Stats -->
          <ng-container *ngIf="dashboardStats.userRole !== 'admin'">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-lg">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Active Projects</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.activeProjects }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-orange-100 rounded-lg">
                  <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Pending Tasks</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.pendingTasks }}</p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-lg">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">Completed Tasks</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.completedTasks }}</p>
                  <div class="mt-1">
                    <div class="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Progress</span>
                      <span>{{ getProgressPercentage(dashboardStats.completedTasks, dashboardStats.totalTasks) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-green-600 h-2 rounded-full transition-all duration-300"
                           [style.width.%]="getProgressPercentage(dashboardStats.completedTasks, dashboardStats.totalTasks)"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div class="flex items-center">
                <div class="p-3 bg-purple-100 rounded-lg">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600">My Teams</p>
                  <p class="text-2xl font-bold text-gray-900">{{ dashboardStats.totalTeams }}</p>
                </div>
              </div>
            </div>
          </ng-container>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <ng-container *ngIf="dashboardStats.userRole === 'admin'; else userActions">
              <a routerLink="/admin/dashboard" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Dashboard</span>
              </a>
              <a routerLink="/admin/projects" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Projects</span>
              </a>
              <a routerLink="/admin/equipes" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Teams</span>
              </a>
              <a routerLink="/admin/profile" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Settings</span>
              </a>
            </ng-container>
            <ng-template #userActions>
              <a routerLink="/projects" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Projects</span>
              </a>
              <a routerLink="/equipes" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Teams</span>
              </a>
              <a routerLink="/plannings" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Calendar</span>
              </a>
              <a routerLink="/profile" class="flex items-center p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                <svg class="w-5 h-5 text-gray-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Profile</span>
              </a>
            </ng-template>
          </div>
        </div>

        <!-- Recent Activity Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

          <!-- Admin Recent Activity -->
          <ng-container *ngIf="dashboardStats.userRole === 'admin'">
            <!-- Recent Users -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Users</h3>
              <div class="space-y-3">
                <div *ngFor="let user of dashboardStats.recentUsers" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span class="text-sm font-semibold text-blue-600">{{ user.username.charAt(0).toUpperCase() }}</span>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">{{ user.username }}</p>
                      <p class="text-xs text-gray-500 capitalize">{{ user.role }}</p>
                    </div>
                  </div>
                  <span class="text-xs text-gray-400">{{ user.createdAt | date:'short' }}</span>
                </div>
                <div *ngIf="!dashboardStats.recentUsers || dashboardStats.recentUsers.length === 0" class="text-center py-8 text-gray-400">
                  <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                  </svg>
                  <p>No recent users</p>
                </div>
              </div>
            </div>

            <!-- Urgent Tasks -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Urgent Tasks</h3>
              <div class="space-y-3">
                <div *ngFor="let task of dashboardStats.urgentTasks" class="p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900">{{ task.title }}</p>
                      <p class="text-xs text-gray-500">{{ task.teamId?.name || 'No team' }}</p>
                    </div>
                    <div class="ml-3 text-right">
                      <span class="inline-block px-2 py-1 text-xs rounded-full"
                            [ngClass]="{
                              'bg-red-100 text-red-800': task.priority === 'high',
                              'bg-yellow-100 text-yellow-800': task.priority === 'medium',
                              'bg-green-100 text-green-800': task.priority === 'low'
                            }">
                        {{ task.priority }}
                      </span>
                      <p class="text-xs text-gray-400 mt-1">{{ task.dueDate | date:'short' }}</p>
                    </div>
                  </div>
                </div>
                <div *ngIf="!dashboardStats.urgentTasks || dashboardStats.urgentTasks.length === 0" class="text-center py-8 text-gray-400">
                  <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0 0h2m-2 0v4a2 2 0 002 2h2a2 2 0 002-2v-4m0 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v6z"></path>
                  </svg>
                  <p>No urgent tasks</p>
                </div>
              </div>
            </div>
          </ng-container>

          <!-- User Recent Activity -->
          <ng-container *ngIf="dashboardStats.userRole !== 'admin'">
            <!-- Recent Projects -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Projects</h3>
              <div class="space-y-3">
                <div *ngFor="let project of dashboardStats.recentProjects" class="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-sm font-medium text-gray-900">{{ project.titre }}</h4>
                      <p class="text-xs text-gray-500 mt-1 line-clamp-2">{{ project.description }}</p>
                    </div>
                    <div class="ml-3 flex-shrink-0">
                      <span class="inline-block w-3 h-3 bg-green-400 rounded-full"></span>
                    </div>
                  </div>
                  <div class="flex justify-between items-center mt-3">
                    <span class="text-xs text-gray-400">{{ project.createdAt | date:'short' }}</span>
                    <span *ngIf="project.dateLimite" class="text-xs px-2 py-1 bg-orange-100 text-orange-800 rounded-full">
                      Due: {{ project.dateLimite | date:'short' }}
                    </span>
                  </div>
                </div>
                <div *ngIf="!dashboardStats.recentProjects || dashboardStats.recentProjects.length === 0" class="text-center py-8 text-gray-400">
                  <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  <p>No recent projects</p>
                </div>
              </div>
            </div>

            <!-- Upcoming Tasks -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Upcoming Tasks</h3>
              <div class="space-y-3">
                <div *ngFor="let task of dashboardStats.upcomingTasks" class="p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900">{{ task.title }}</p>
                      <p class="text-xs text-gray-500">{{ task.teamId?.name || 'Personal' }}</p>
                    </div>
                    <div class="ml-3 text-right">
                      <span class="inline-block px-2 py-1 text-xs rounded-full"
                            [ngClass]="{
                              'bg-red-100 text-red-800': task.priority === 'high',
                              'bg-yellow-100 text-yellow-800': task.priority === 'medium',
                              'bg-green-100 text-green-800': task.priority === 'low'
                            }">
                        {{ task.priority }}
                      </span>
                      <p class="text-xs text-gray-400 mt-1">{{ task.dueDate | date:'short' }}</p>
                    </div>
                  </div>
                </div>
                <div *ngIf="!dashboardStats.upcomingTasks || dashboardStats.upcomingTasks.length === 0" class="text-center py-8 text-gray-400">
                  <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <p>No upcoming tasks</p>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
