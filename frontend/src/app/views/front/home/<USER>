<!-- Modern Gradient Background -->
<div class="min-h-screen bg-gradient-to-br from-purple-400 via-purple-500 to-blue-600 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-400/20 via-transparent to-blue-600/20"></div>
    <div class="absolute top-20 right-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 left-20 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
  </div>

  <div class="relative z-10 container mx-auto px-4 py-16">
    <!-- Hero Section -->
    <section class="text-center text-white mb-20">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
          <ng-container *ngIf="!authService.userLoggedIn(); else welcomeBack">
            Effortless Task Management<br>
            <span class="text-white/90">for Teams and Individuals</span>
          </ng-container>
          <ng-template #welcomeBack>
            Welcome Back, {{ authService.getCurrentUser()?.username }}!
            <span *ngIf="isAdmin()" class="block text-2xl text-white/80 mt-2">(Administrator)</span>
          </ng-template>
        </h1>

        <p class="text-xl md:text-2xl mb-8 text-white/90 max-w-2xl mx-auto leading-relaxed">
          <ng-container *ngIf="!authService.userLoggedIn(); else userStats">
            Our service caters to both teams and individuals, ensuring everyone can stay organized and focused.
          </ng-container>
          <ng-template #userStats>
            <ng-container *ngIf="loading">
              <span class="animate-pulse">Loading your dashboard...</span>
            </ng-container>
            <ng-container *ngIf="!loading && error">
              <span class="text-red-300">{{ error }}</span>
            </ng-container>
            <ng-container *ngIf="!loading && !error && dashboardStats">
              <ng-container *ngIf="dashboardStats.userRole === 'admin'; else regularUserStats">
                You manage <strong>{{ dashboardStats.totalUsers }} users</strong>,
                <strong>{{ dashboardStats.totalProjects }} projects</strong>, and
                <strong>{{ dashboardStats.pendingTasks }} pending tasks</strong>.
              </ng-container>
              <ng-template #regularUserStats>
                You have <strong>{{ dashboardStats.activeProjects }} active projects</strong>
                with <strong>{{ dashboardStats.pendingTasks }} pending tasks</strong>.
              </ng-template>
            </ng-container>
          </ng-template>
        </p>

        <!-- Email Input and CTA -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12 max-w-md mx-auto">
          <ng-container *ngIf="!authService.userLoggedIn(); else dashboardLink">
            <input
              type="email"
              placeholder="Enter your email"
              class="flex-1 px-6 py-4 rounded-full text-gray-800 placeholder-gray-500 border-0 focus:ring-4 focus:ring-white/30 outline-none text-lg"
            >
            <a
              routerLink="/registeruser"
              class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
              Try it free
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </ng-container>

          <ng-template #dashboardLink>
            <div class="flex flex-col sm:flex-row gap-4">
              <a *ngIf="isAdmin()"
                 routerLink="/admin/dashboard"
                 class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition-all duration-300 shadow-lg">
                Go to Dashboard
              </a>
              <a routerLink="/projects"
                 class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300">
                View Projects
              </a>
            </div>
          </ng-template>
        </div>
      </div>
    </section>

    <!-- Enhanced Dashboard Section for Logged-in Users -->
    <section *ngIf="authService.userLoggedIn() && !loading && !error && dashboardStats" class="mb-20">

      <!-- Dashboard Header -->
      <div class="flex justify-between items-center mb-8">
        <div>
          <h2 class="text-3xl font-bold text-white mb-2">
            {{ dashboardStats.userRole === 'admin' ? 'Admin Dashboard' : 'Your Dashboard' }}
          </h2>
          <p class="text-white/60">
            {{ dashboardStats.userRole === 'admin' ? 'System overview and management' : 'Track your progress and manage your work' }}
          </p>
        </div>
        <button
          (click)="refreshDashboard()"
          class="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <span class="text-sm">Refresh</span>
        </button>
      </div>

      <!-- Admin Dashboard -->
      <div *ngIf="dashboardStats.userRole === 'admin'" class="space-y-8">

        <!-- Admin Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-blue-500/20 rounded-lg">
                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.totalUsers }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Total Users</h3>
            <p class="text-xs text-green-400 mt-1">{{ dashboardStats.activeUsers }} active</p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-purple-500/20 rounded-lg">
                <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.totalProjects }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Total Projects</h3>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-orange-500/20 rounded-lg">
                <svg class="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0 0h2m-2 0v4a2 2 0 002 2h2a2 2 0 002-2v-4m0 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v6z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.pendingTasks }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Pending Tasks</h3>
            <p class="text-xs text-green-400 mt-1">{{ dashboardStats.completedTasks }} completed</p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-green-500/20 rounded-lg">
                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.totalTeams }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Total Teams</h3>
          </div>
        </div>

        <!-- Admin Quick Actions -->
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20 mb-8">
          <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a routerLink="/admin/dashboard" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              <span class="text-sm">Dashboard</span>
            </a>
            <a routerLink="/admin/projects" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <span class="text-sm">Projects</span>
            </a>
            <a routerLink="/admin/equipes" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <span class="text-sm">Teams</span>
            </a>
            <a routerLink="/admin/profile" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="text-sm">Settings</span>
            </a>
          </div>
        </div>

        <!-- Recent Activity for Admin -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Recent Users -->
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <h3 class="text-lg font-semibold mb-4">Recent Users</h3>
            <div class="space-y-3">
              <div *ngFor="let user of dashboardStats.recentUsers" class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-xs font-bold">
                    {{ user.username.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <p class="font-medium">{{ user.username }}</p>
                    <p class="text-sm text-white/60 capitalize">{{ user.role }}</p>
                  </div>
                </div>
                <span class="text-xs text-white/40">{{ user.createdAt | date:'short' }}</span>
              </div>
              <div *ngIf="dashboardStats.recentUsers?.length === 0" class="text-center py-8 text-white/40">
                <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                <p>No recent users</p>
              </div>
            </div>
          </div>

          <!-- Urgent Tasks -->
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <h3 class="text-lg font-semibold mb-4">Urgent Tasks</h3>
            <div class="space-y-3">
              <div *ngFor="let task of dashboardStats.urgentTasks" class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div>
                  <p class="font-medium">{{ task.title }}</p>
                  <p class="text-sm text-white/60">{{ task.teamId?.name || 'No team' }}</p>
                </div>
                <div class="text-right">
                  <span class="text-xs px-2 py-1 rounded-full"
                        [ngClass]="{
                          'bg-red-500/20 text-red-400': task.priority === 'high',
                          'bg-yellow-500/20 text-yellow-400': task.priority === 'medium',
                          'bg-green-500/20 text-green-400': task.priority === 'low'
                        }">
                    {{ task.priority }}
                  </span>
                  <p class="text-xs text-white/40 mt-1">{{ task.dueDate | date:'short' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Regular User Dashboard -->
      <div *ngIf="dashboardStats.userRole !== 'admin'" class="space-y-8">

        <!-- User Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-blue-500/20 rounded-lg">
                <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.activeProjects }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Active Projects</h3>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-orange-500/20 rounded-lg">
                <svg class="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.pendingTasks }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Pending Tasks</h3>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-green-500/20 rounded-lg">
                <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.completedTasks }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">Completed Tasks</h3>
            <div class="mt-3">
              <div class="flex justify-between text-xs text-white/60 mb-1">
                <span>Progress</span>
                <span>{{ getProgressPercentage(dashboardStats.completedTasks, dashboardStats.totalTasks) }}%</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-green-400 h-2 rounded-full transition-all duration-300"
                     [style.width.%]="getProgressPercentage(dashboardStats.completedTasks, dashboardStats.totalTasks)"></div>
              </div>
            </div>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <div class="flex items-center justify-between mb-4">
              <div class="p-3 bg-purple-500/20 rounded-lg">
                <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <span class="text-2xl font-bold">{{ dashboardStats.totalTeams }}</span>
            </div>
            <h3 class="text-sm font-medium text-white/80">My Teams</h3>
          </div>
        </div>

        <!-- User Quick Actions -->
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20 mb-8">
          <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a routerLink="/projects" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <span class="text-sm">Projects</span>
            </a>
            <a routerLink="/equipes" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <span class="text-sm">Teams</span>
            </a>
            <a routerLink="/plannings" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <span class="text-sm">Calendar</span>
            </a>
            <a routerLink="/profile" class="flex items-center gap-3 p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <span class="text-sm">Profile</span>
            </a>
          </div>
        </div>

        <!-- User Activity Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Recent Projects -->
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <h3 class="text-lg font-semibold mb-4">Recent Projects</h3>
            <div class="space-y-3">
              <div *ngFor="let project of dashboardStats.recentProjects" class="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="font-medium">{{ project.titre }}</h4>
                    <p class="text-sm text-white/60 mt-1 line-clamp-2">{{ project.description }}</p>
                  </div>
                  <div class="ml-3 flex-shrink-0">
                    <span class="inline-block w-3 h-3 bg-green-400 rounded-full"></span>
                  </div>
                </div>
                <div class="flex justify-between items-center mt-3">
                  <span class="text-xs text-white/40">{{ project.createdAt | date:'short' }}</span>
                  <span *ngIf="project.dateLimite" class="text-xs px-2 py-1 bg-orange-500/20 text-orange-400 rounded-full">
                    Due: {{ project.dateLimite | date:'short' }}
                  </span>
                </div>
              </div>
              <div *ngIf="dashboardStats.recentProjects?.length === 0" class="text-center py-8 text-white/40">
                <svg class="w-12 h-12 mx-auto mb-3 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <p>No recent projects</p>
              </div>
            </div>
          </div>

          <!-- Upcoming Tasks -->
          <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
            <h3 class="text-lg font-semibold mb-4">Upcoming Tasks</h3>
            <div class="space-y-3">
              <div *ngFor="let task of dashboardStats.upcomingTasks" class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div>
                  <p class="font-medium">{{ task.title }}</p>
                  <p class="text-sm text-white/60">{{ task.teamId?.name || 'Personal' }}</p>
                </div>
                <div class="text-right">
                  <span class="text-xs px-2 py-1 rounded-full"
                        [ngClass]="{
                          'bg-red-500/20 text-red-400': task.priority === 'high',
                          'bg-yellow-500/20 text-yellow-400': task.priority === 'medium',
                          'bg-green-500/20 text-green-400': task.priority === 'low'
                        }">
                    {{ task.priority }}
                  </span>
                  <p class="text-xs text-white/40 mt-1">{{ task.dueDate | date:'short' }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Team Performance -->
        <div *ngIf="dashboardStats.teamPerformance?.length > 0" class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 text-white border border-white/20">
          <h3 class="text-lg font-semibold mb-4">Team Performance</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div *ngFor="let team of dashboardStats.teamPerformance" class="p-4 bg-white/5 rounded-lg">
              <h4 class="font-medium mb-2">{{ team.team }}</h4>
              <p class="text-sm text-white/60 mb-3">{{ team.memberCount }} members</p>
              <div class="space-y-2">
                <div *ngFor="let stat of team.taskStats" class="flex justify-between text-sm">
                  <span class="capitalize">{{ stat._id }}:</span>
                  <span>{{ stat.count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="text-center text-white mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6">
        {{ authService.userLoggedIn() ? 'Ready to boost your productivity?' : 'Ready to boost your productivity?' }}
      </h2>
      <div class="flex flex-col sm:flex-row items-center justify-center gap-4 max-w-md mx-auto">
        <ng-container *ngIf="!authService.userLoggedIn(); else loggedInCta">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-6 py-4 rounded-full text-gray-800 placeholder-gray-500 border-0 focus:ring-4 focus:ring-white/30 outline-none text-lg"
          >
          <a
            routerLink="/registeruser"
            class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
            Try it free
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </ng-container>

        <ng-template #loggedInCta>
          <div class="flex flex-col sm:flex-row gap-4">
            <a *ngIf="isAdmin()"
               routerLink="/admin/dashboard"
               class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition-all duration-300 shadow-lg">
              Go to Dashboard
            </a>
            <a routerLink="/projects"
               class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300">
              View Projects
            </a>
          </div>
        </ng-template>
      </div>
    </section>
  </div>
</div>