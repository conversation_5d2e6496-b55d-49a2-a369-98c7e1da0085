<!-- Modern Gradient Background -->
<div class="min-h-screen bg-gradient-to-br from-purple-400 via-purple-500 to-blue-600 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-400/20 via-transparent to-blue-600/20"></div>
    <div class="absolute top-20 right-20 w-64 h-64 bg-white/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-20 left-20 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
  </div>

  <div class="relative z-10 container mx-auto px-4 py-16">
    <!-- Hero Section -->
    <section class="text-center text-white mb-20">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
          <ng-container *ngIf="!authService.userLoggedIn(); else welcomeBack">
            Effortless Task Management<br>
            <span class="text-white/90">for Teams and Individuals</span>
          </ng-container>
          <ng-template #welcomeBack>
            Welcome Back, {{ authService.getCurrentUser()?.username }}!
            <span *ngIf="isAdmin()" class="block text-2xl text-white/80 mt-2">(Administrator)</span>
          </ng-template>
        </h1>

        <p class="text-xl md:text-2xl mb-8 text-white/90 max-w-2xl mx-auto leading-relaxed">
          <ng-container *ngIf="!authService.userLoggedIn(); else userStats">
            Our service caters to both teams and individuals, ensuring everyone can stay organized and focused.
          </ng-container>
          <ng-template #userStats>
            <ng-container *ngIf="loading">
              <span class="animate-pulse">Loading your dashboard...</span>
            </ng-container>
            <ng-container *ngIf="!loading && error">
              <span class="text-red-300">{{ error }}</span>
            </ng-container>
            <ng-container *ngIf="!loading && !error && dashboardStats">
              <ng-container *ngIf="dashboardStats.userRole === 'admin'; else regularUserStats">
                You manage <strong>{{ dashboardStats.totalUsers }} users</strong>,
                <strong>{{ dashboardStats.totalProjects }} projects</strong>, and
                <strong>{{ dashboardStats.pendingTasks }} pending tasks</strong>.
              </ng-container>
              <ng-template #regularUserStats>
                You have <strong>{{ dashboardStats.activeProjects }} active projects</strong>
                with <strong>{{ dashboardStats.pendingTasks }} pending tasks</strong>.
              </ng-template>
            </ng-container>
          </ng-template>
        </p>

        <!-- Email Input and CTA -->
        <div class="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12 max-w-md mx-auto">
          <ng-container *ngIf="!authService.userLoggedIn(); else dashboardLink">
            <input
              type="email"
              placeholder="Enter your email"
              class="flex-1 px-6 py-4 rounded-full text-gray-800 placeholder-gray-500 border-0 focus:ring-4 focus:ring-white/30 outline-none text-lg"
            >
            <a
              routerLink="/registeruser"
              class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
              Try it free
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </ng-container>

          <ng-template #dashboardLink>
            <div class="flex flex-col sm:flex-row gap-4">
              <a *ngIf="isAdmin()"
                 routerLink="/admin/dashboard"
                 class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition-all duration-300 shadow-lg">
                Go to Dashboard
              </a>
              <a routerLink="/projects"
                 class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300">
                View Projects
              </a>
            </div>
          </ng-template>
        </div>
      </div>
    </section>



    <!-- Final CTA Section -->
    <section class="text-center text-white mb-16">
      <h2 class="text-3xl md:text-4xl font-bold mb-6">
        {{ authService.userLoggedIn() ? 'Ready to boost your productivity?' : 'Ready to boost your productivity?' }}
      </h2>
      <div class="flex flex-col sm:flex-row items-center justify-center gap-4 max-w-md mx-auto">
        <ng-container *ngIf="!authService.userLoggedIn(); else loggedInCta">
          <input
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-6 py-4 rounded-full text-gray-800 placeholder-gray-500 border-0 focus:ring-4 focus:ring-white/30 outline-none text-lg"
          >
          <a
            routerLink="/registeruser"
            class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300 flex items-center gap-2 whitespace-nowrap">
            Try it free
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </ng-container>

        <ng-template #loggedInCta>
          <div class="flex flex-col sm:flex-row gap-4">
            <a *ngIf="isAdmin()"
               routerLink="/admin/dashboard"
               class="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-50 transition-all duration-300 shadow-lg">
              Go to Dashboard
            </a>
            <a routerLink="/projects"
               class="bg-black text-white px-8 py-4 rounded-full font-semibold hover:bg-gray-800 transition-all duration-300">
              View Projects
            </a>
          </div>
        </ng-template>
      </div>
    </section>
  </div>
</div>