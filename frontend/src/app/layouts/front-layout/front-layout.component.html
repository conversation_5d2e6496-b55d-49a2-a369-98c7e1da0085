<div
  class="flex h-screen main-grid-container text-[#6d6870] dark:text-[#a0a0a0] futuristic-layout"
  [class.dark]="isDarkMode$ | async"
>
  <!-- Background Grid -->
  <div class="background-grid"></div>

  <!-- Sidebar -->
  <div class="hidden md:flex md:flex-shrink-0">
    <div
      class="flex flex-col w-64 bg-white dark:bg-[#1e1e1e] border-r border-[#edf1f4] dark:border-[#2a2a2a] backdrop-blur-sm"
    >
      <div
        class="flex items-center justify-center h-16 px-4 relative overflow-hidden"
      >
        <!-- Decorative elements -->
        <div
          class="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-transparent rounded-full"
        ></div>
        <div
          class="absolute -bottom-6 -right-6 w-12 h-12 bg-gradient-to-tl from-[#4f5fad]/20 to-transparent rounded-full"
        ></div>

        <div class="flex items-center relative z-10">
          <div class="relative">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-8 w-8 text-[#4f5fad] dark:text-[#6d78c9] transform rotate-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <!-- Glow effect -->
            <div
              class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10"
            ></div>
          </div>
          <span
            class="ml-2 text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent"
            >DevBridge</span
          >
        </div>
      </div>
      <div class="flex flex-col flex-grow px-4 py-4">
        <nav class="flex-1 space-y-2">
          <!-- Navigation Items - Réorganisés avec Accueil en premier -->

          <!-- Accueil button -->
          <a
            *ngIf="authService.userLoggedIn()"
            routerLink="/"
            routerLinkActive="active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium"
            [routerLinkActiveOptions]="{ exact: true }"
            class="sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="fas fa-home h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Accueil</span>
            </div>
          </a>

          <!-- Projects -->
          <a
            *ngIf="authService.userLoggedIn()"
            routerLink="/projects"
            routerLinkActive="active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium"
            class="sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="fas fa-rocket h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Projects</span>
            </div>
          </a>

          <!-- Plannings -->
          <a
            *ngIf="authService.userLoggedIn()"
            routerLink="/plannings"
            routerLinkActive="active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium"
            class="sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="far fa-calendar-check h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Plannings</span>
            </div>
          </a>

          <!-- Réunions -->
          <a
            *ngIf="authService.userLoggedIn()"
            routerLink="/reunions"
            routerLinkActive="active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium"
            class="sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="fas fa-users-cog h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Réunions</span>
            </div>
          </a>

          <!-- Messages -->
          <a
            *ngIf="authService.userLoggedIn()"
            routerLink="/messages"
            routerLinkActive="active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium"
            class="sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="far fa-comment-dots h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Messages</span>
            </div>
          </a>
          <!-- Equipes -->
          <a
            *ngIf="authService.userLoggedIn()"
            routerLink="/equipes"
            routerLinkActive="active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium"
            class="sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="fas fa-users h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Equipes</span>
            </div>
          </a>
          <!-- Séparateur -->
          <div
            class="border-t border-[#edf1f4] dark:border-[#2a2a2a] my-2"
          ></div>

          <!-- Go to Dashboard button - uniquement pour admin et teacher -->
          <a
            *ngIf="
              authService.userLoggedIn() &&
              (currentUser?.role === 'admin' || currentUser?.role === 'teacher')
            "
            routerLink="/admin/dashboard"
            routerLinkActive="active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium"
            class="sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center">
              <div class="relative">
                <i
                  class="fas fa-tachometer-alt h-5 w-5 mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:text-[#7826b5] dark:group-hover:text-[#9d4edd] transition-all group-hover:scale-110"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span class="relative">Go to Dashboard</span>
            </div>
          </a>
        </nav>
      </div>
    </div>
  </div>

  <!-- La grille est maintenant gérée par la classe background-grid -->
  <!-- Header -->
  <header
    class="fixed top-0 left-0 right-0 bg-white dark:bg-[#1e1e1e] shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] z-50 backdrop-blur-sm border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
      <!-- Decorative elements -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          class="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent"
        ></div>
        <div
          class="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent"
        ></div>
      </div>

      <div class="flex items-center justify-between h-16 relative z-10">
        <!-- Logo and main nav (left side) -->
        <div class="flex items-center">
          <!-- Mobile menu button -->
          <button
            (click)="toggleSidebar()"
            class="md:hidden flex items-center justify-center h-8 px-3 rounded-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] focus:outline-none transition-colors relative group overflow-hidden"
            aria-label="Toggle menu"
          >
            <div
              class="absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-md blur-md"
            ></div>
            <svg
              class="h-5 w-5 relative z-10"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
            <span
              class="text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-2 relative z-10"
              >Menu</span
            >
          </button>

          <!-- Logo -->
          <a routerLink="/" class="flex-shrink-0 flex items-center group">
            <div class="relative">
              <h1
                class="text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent group-hover:scale-105 transition-transform"
              >
                DevBridge
              </h1>
              <!-- Glow effect -->
              <div
                class="absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 blur-xl rounded-full transform scale-150 -z-10 opacity-0 group-hover:opacity-100 transition-opacity"
              ></div>
            </div>
            <span
              class="ml-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] hidden md:block"
              >Project Management Suite</span
            >
          </a>

          <!-- Navigation - Removed and moved to sidebar -->
        </div>

        <!-- Right side -->
        <div class="flex items-center">
          <ng-container *ngIf="authService.userLoggedIn(); else authButtons">
            <div class="ml-4 flex items-center md:ml-6">
              <!-- Bouton de notification simplifié -->
              <a
                *ngIf="authService.userLoggedIn()"
                routerLink="/notifications"
                class="flex items-center justify-center h-10 px-4 rounded-xl bg-white/20 border border-white/30 text-white mr-2 transition-all duration-300 hover:bg-white/30 backdrop-blur-sm"
                aria-label="Notifications"
              >
                <!-- Contenu du bouton avec icône et compteur -->
                <div class="flex items-center">
                  <i class="far fa-bell text-lg transition-transform mr-2"></i>

                  <!-- Compteur de notifications avec couleur orange fluo -->
                  <div
                    class="flex items-center justify-center h-6 min-w-6 px-1.5 rounded-md bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white font-bold text-xs shadow-md animate-pulse"
                  >
                    {{
                      unreadNotificationsCount > 0
                        ? unreadNotificationsCount > 99
                          ? "99+"
                          : unreadNotificationsCount
                        : "0"
                    }}
                  </div>
                </div>
              </a>

              <!-- Professional Dark Mode Toggle Button -->
              <button
                (click)="toggleDarkMode()"
                class="flex items-center justify-center h-10 w-10 rounded-full bg-white/20 border border-white/30 hover:bg-white/30 text-white mr-3 transition-all duration-200 backdrop-blur-sm group"
                [attr.aria-label]="(isDarkMode$ | async) ? 'Switch to light mode' : 'Switch to dark mode'"
              >
                <!-- Light mode icon (sun) -->
                <svg
                  *ngIf="!(isDarkMode$ | async)"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 group-hover:scale-110 transition-transform duration-200"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>

                <!-- Dark mode icon (moon) -->
                <svg
                  *ngIf="isDarkMode$ | async"
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 group-hover:scale-110 transition-transform duration-200"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                </svg>
              </button>

              <!-- Logout Button -->
              <button
                (click)="logout()"
                class="flex items-center justify-center h-8 w-8 rounded-full bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#ff6b69] dark:text-[#ff8785] mr-3 transition-all duration-300 relative overflow-hidden group"
                aria-label="Logout"
              >
                <!-- Animated border -->
                <div class="absolute inset-0 rounded-full overflow-hidden">
                  <div
                    class="absolute inset-0 rounded-full border border-[#ff6b69]/20 dark:border-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity"
                  ></div>
                  <div
                    class="absolute -inset-1 bg-gradient-to-r from-[#ff6b69]/0 via-[#ff6b69]/30 to-[#ff6b69]/0 dark:from-[#ff8785]/0 dark:via-[#ff8785]/30 dark:to-[#ff8785]/0 opacity-0 group-hover:opacity-100 blur-sm animate-shine"
                  ></div>
                </div>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#ff6b69]/10 dark:bg-[#ff8785]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md"
                ></div>
                <i
                  class="fas fa-sign-out-alt relative z-10 group-hover:scale-110 transition-transform"
                ></i>
              </button>

              <!-- Profile Button -->
              <a
                routerLink="/profile"
                class="flex items-center bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#4f5fad] dark:text-[#6d78c9] px-3 py-2 rounded-lg transition-all group"
              >
                <span class="sr-only">Profile</span>
                <span
                  class="hidden md:inline-block mr-2 text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-105 transition-transform"
                >
                  {{ username }}
                </span>
                <div
                  class="h-8 w-8 rounded-full overflow-hidden border-2 border-[#4f5fad] dark:border-[#6d78c9] flex items-center justify-center relative group-hover:border-[#3d4a85] dark:group-hover:border-[#4f5fad] transition-colors"
                >
                  <!-- Glow effect -->
                  <div
                    class="absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md"
                  ></div>
                  <img
                    [src]="imageProfile"
                    alt="Profile"
                    class="h-full w-full object-cover"
                  />
                </div>
              </a>
            </div>
          </ng-container>

          <!-- Auth Buttons for non-logged in users -->
          <ng-template #authButtons>
            <div class="flex items-center space-x-4">
              <!-- Dark Mode Toggle -->
              <button
                (click)="toggleDarkMode()"
                class="relative p-2 rounded-lg bg-white/10 dark:bg-black/20 backdrop-blur-sm border border-white/20 dark:border-white/10 hover:bg-white/20 dark:hover:bg-black/30 transition-all duration-300 group"
                [attr.aria-label]="(isDarkMode$ | async) ? 'Switch to light mode' : 'Switch to dark mode'"
              >
                <!-- Background glow effect -->
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#4f5fad]/20 to-[#7826b5]/20 dark:from-[#6d78c9]/20 dark:to-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg blur-xl"
                ></div>

                <!-- Sun icon (visible in dark mode) -->
                <i
                  class="fas fa-sun text-yellow-400 dark:text-yellow-300 transition-all duration-300 relative z-10"
                  [class.opacity-0]="!(isDarkMode$ | async)"
                  [class.opacity-100]="(isDarkMode$ | async)"
                  [class.rotate-180]="!(isDarkMode$ | async)"
                  [class.rotate-0]="(isDarkMode$ | async)"
                ></i>

                <!-- Moon icon (visible in light mode) -->
                <i
                  class="fas fa-moon text-slate-600 dark:text-slate-400 transition-all duration-300 absolute inset-0 flex items-center justify-center"
                  [class.opacity-100]="!(isDarkMode$ | async)"
                  [class.opacity-0]="(isDarkMode$ | async)"
                  [class.rotate-0]="!(isDarkMode$ | async)"
                  [class.rotate-180]="(isDarkMode$ | async)"
                ></i>
              </button>

              <a
                routerLink="/login"
                class="inline-flex items-center relative overflow-hidden group"
              >
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105"
                ></div>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300"
                ></div>
                <span
                  class="relative flex items-center text-white font-medium py-2 px-4 rounded-lg transition-all"
                >
                  <i class="fas fa-sign-in-alt mr-2"></i>
                  Connexion
                </span>
              </a>

              <a
                routerLink="/signup"
                class="inline-flex items-center relative overflow-hidden group"
              >
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] rounded-lg transition-transform duration-300 group-hover:scale-105"
                ></div>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300"
                ></div>
                <span
                  class="relative flex items-center text-white font-medium py-2 px-4 rounded-lg transition-all"
                >
                  <i class="fas fa-user-plus mr-2"></i>
                  Inscription
                </span>
              </a>
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </header>

  <!-- Barre latérale mobile -->
  <div
    class="fixed inset-0 z-40 md:hidden"
    [class.hidden]="!sidebarOpen"
    (click)="sidebarOpen && toggleSidebar()"
  >
    <!-- Overlay backdrop with blur effect -->
    <div
      class="absolute inset-0 bg-black/30 dark:bg-black/50 backdrop-blur-sm"
    ></div>

    <!-- Contenu de la barre latérale -->
    <div
      class="fixed inset-y-0 left-0 max-w-xs w-full bg-white dark:bg-[#1e1e1e] shadow-lg dark:shadow-[0_0_30px_rgba(0,0,0,0.3)] transform transition-transform duration-300 ease-in-out border-r border-[#edf1f4]/50 dark:border-[#2a2a2a]"
      [class.translate-x-0]="sidebarOpen"
      [class.-translate-x-full]="!sidebarOpen"
      (click)="$event.stopPropagation()"
    >
      <div class="flex flex-col h-full relative">
        <!-- Decorative elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
          <div
            class="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent"
          ></div>
          <div
            class="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent"
          ></div>
          <div
            class="absolute top-[10%] left-[5%] w-32 h-32 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl"
          ></div>
          <div
            class="absolute bottom-[10%] right-[5%] w-40 h-40 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl"
          ></div>
        </div>

        <!-- En-tête de la barre latérale -->
        <div
          class="flex items-center justify-between px-4 py-3 border-b border-[#edf1f4] dark:border-[#2a2a2a] relative z-10"
        >
          <div>
            <h3
              class="text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent"
            >
              DevBridge
            </h3>
            <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
              Project Management Suite
            </p>
          </div>
          <button
            (click)="toggleSidebar()"
            class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] p-2 rounded-full hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-colors relative group"
            aria-label="Close menu"
          >
            <div
              class="absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-full blur-md"
            ></div>
            <i class="fas fa-times relative z-10"></i>
          </button>
        </div>

        <!-- Navigation de la barre latérale -->
        <nav
          class="flex-1 px-2 py-4 space-y-1 overflow-y-auto bg-white dark:bg-[#1e1e1e] relative z-10"
        >
          <!-- Navigation Items - Réorganisés avec Accueil en premier -->
          <a
            *ngFor="
              let item of [
                {
                  route: '/',
                  icon: 'fas fa-home',
                  text: 'Accueil'
                },
                {
                  route: '/projects',
                  icon: 'fas fa-rocket',
                  text: 'Projects'
                },
                {
                  route: '/plannings',
                  icon: 'far fa-calendar-check',
                  text: 'Plannings'
                },
                {
                  route: '/reunions',
                  icon: 'fas fa-users-cog',
                  text: 'Réunions'
                },
                {
                  route: '/messages',
                  icon: 'far fa-comment-dots',
                  text: 'Messages'
                },
                {
                  route: '/equipes',
                  icon: 'fas fa-users',
                  text: 'Equipes'
                },
                {
                  route: '/notifications',
                  icon: 'far fa-bell',
                  text: 'Notifications',
                  badge: unreadNotificationsCount
                }
              ]
            "
            routerLink="{{ item.route }}"
            (click)="toggleSidebar()"
            class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden"
            routerLinkActive="bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]"
            [routerLinkActiveOptions]="{
              exact: item.route === '/' ? true : false
            }"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center w-full">
              <div class="relative">
                <i
                  class="{{
                    item.icon
                  }} h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span>{{ item.text }}</span>
              <div
                *ngIf="item.badge && item.badge > 0"
                class="ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse"
              >
                {{ item.badge > 99 ? "99+" : item.badge }}
              </div>
            </div>
          </a>

          <!-- Go to Dashboard button - uniquement pour admin et teacher -->
          <a
            *ngIf="
              authService.userLoggedIn() &&
              (currentUser?.role === 'admin' || currentUser?.role === 'teacher')
            "
            routerLink="/admin/dashboard"
            (click)="toggleSidebar()"
            class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2"
          >
            <!-- Hover effect -->
            <span
              class="absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity"
            ></span>

            <div class="relative z-10 flex items-center w-full">
              <div class="relative">
                <i
                  class="fas fa-tachometer-alt h-5 w-5 mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:text-[#7826b5] dark:group-hover:text-[#9d4edd] transition-colors"
                ></i>
                <!-- Glow effect -->
                <div
                  class="absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                ></div>
              </div>
              <span>Go to Dashboard</span>
            </div>
          </a>

          <div
            class="border-t border-[#edf1f4] dark:border-[#2a2a2a] my-2"
          ></div>

          <ng-container *ngIf="!authService.userLoggedIn()">
            <a
              routerLink="/login"
              (click)="toggleSidebar()"
              class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden"
            >
              <!-- Hover effect -->
              <span
                class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
              ></span>

              <div class="relative z-10 flex items-center">
                <div class="relative">
                  <i
                    class="fas fa-sign-in-alt mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-110 transition-transform"
                  ></i>
                  <!-- Glow effect -->
                  <div
                    class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                  ></div>
                </div>
                <span>Connexion</span>
              </div>
            </a>

            <a
              routerLink="/signup"
              (click)="toggleSidebar()"
              class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden"
            >
              <!-- Hover effect -->
              <span
                class="absolute inset-0 w-1 bg-gradient-to-b from-[#7826b5] to-[#9d4edd] dark:from-[#7826b5] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity"
              ></span>

              <div class="relative z-10 flex items-center">
                <div class="relative">
                  <i
                    class="fas fa-user-plus mr-3 text-[#7826b5] dark:text-[#9d4edd] group-hover:scale-110 transition-transform"
                  ></i>
                  <!-- Glow effect -->
                  <div
                    class="absolute inset-0 bg-[#7826b5]/20 dark:bg-[#9d4edd]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                  ></div>
                </div>
                <span>Inscription</span>
              </div>
            </a>

            <!-- Dark Mode Toggle for non-logged in users -->
            <button
              (click)="toggleDarkMode()"
              class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden w-full"
              [attr.aria-label]="(isDarkMode$ | async) ? 'Switch to light mode' : 'Switch to dark mode'"
            >
              <!-- Hover effect -->
              <span
                class="absolute inset-0 w-1 bg-gradient-to-b from-[#4f5fad] to-[#7826b5] dark:from-[#6d78c9] dark:to-[#9d4edd] opacity-0 group-hover:opacity-100 transition-opacity"
              ></span>

              <div class="relative z-10 flex items-center">
                <div class="relative mr-3">
                  <!-- Sun icon (visible in dark mode) -->
                  <i
                    class="fas fa-sun text-yellow-400 dark:text-yellow-300 transition-all duration-300"
                    [class.opacity-0]="!(isDarkMode$ | async)"
                    [class.opacity-100]="(isDarkMode$ | async)"
                    [class.rotate-180]="!(isDarkMode$ | async)"
                    [class.rotate-0]="(isDarkMode$ | async)"
                  ></i>

                  <!-- Moon icon (visible in light mode) -->
                  <i
                    class="fas fa-moon text-slate-600 dark:text-slate-400 transition-all duration-300 absolute inset-0 flex items-center justify-center"
                    [class.opacity-100]="!(isDarkMode$ | async)"
                    [class.opacity-0]="(isDarkMode$ | async)"
                    [class.rotate-0]="!(isDarkMode$ | async)"
                    [class.rotate-180]="(isDarkMode$ | async)"
                  ></i>

                  <!-- Glow effect -->
                  <div
                    class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                  ></div>
                </div>
                <span>{{ (isDarkMode$ | async) ? 'Mode Clair' : 'Mode Sombre' }}</span>
              </div>
            </button>
          </ng-container>

          <ng-container *ngIf="authService.userLoggedIn()">
            <a
              routerLink="/profile"
              (click)="toggleSidebar()"
              class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden"
            >
              <!-- Hover effect -->
              <span
                class="absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity"
              ></span>

              <div class="relative z-10 flex items-center">
                <div class="relative">
                  <i
                    class="fas fa-user mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:scale-110 transition-transform"
                  ></i>
                  <!-- Glow effect -->
                  <div
                    class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                  ></div>
                </div>
                <span>Mon Profil</span>
              </div>
            </a>

            <a
              (click)="logout(); toggleSidebar()"
              class="flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] dark:hover:text-[#ff8785] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden cursor-pointer"
            >
              <!-- Hover effect -->
              <span
                class="absolute inset-0 w-1 bg-gradient-to-b from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] opacity-0 group-hover:opacity-100 transition-opacity"
              ></span>

              <div class="relative z-10 flex items-center">
                <div class="relative">
                  <i
                    class="fas fa-sign-out-alt mr-3 text-[#ff6b69] dark:text-[#ff8785] group-hover:scale-110 transition-transform"
                  ></i>
                  <!-- Glow effect -->
                  <div
                    class="absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full"
                  ></div>
                </div>
                <span>Déconnexion</span>
              </div>
            </a>
          </ng-container>
        </nav>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- Contenu principal -->
    <main
      class="flex-1 overflow-y-auto bg-[#edf1f4] dark:bg-[#121212] pt-16 pb-6 relative"
    >
      <!-- Background decorative elements -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          class="absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl"
        ></div>
        <div
          class="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl"
        ></div>
      </div>

      <!-- Message de statut -->
      <div
        *ngIf="messageFromRedirect"
        class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4"
      >
        <div
          class="bg-white dark:bg-[#1e1e1e] border-l-4 border-[#ff8c00] dark:border-[#ff6b00] rounded-lg p-4 mb-6 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] backdrop-blur-sm"
        >
          <div class="flex items-center">
            <div class="relative">
              <i
                class="fas fa-info-circle text-[#ff8c00] dark:text-[#ff6b00] text-lg mr-3"
              ></i>
              <!-- Glow effect -->
              <div
                class="absolute inset-0 bg-[#ff8c00]/30 dark:bg-[#ff6b00]/30 blur-xl rounded-full transform scale-150 -z-10 animate-pulse"
              ></div>
            </div>
            <p
              class="ml-3 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]"
            >
              {{ messageFromRedirect }}
            </p>
          </div>
        </div>
      </div>

      <!-- Router outlet -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <router-outlet></router-outlet>
      </div>
    </main>
  </div>
</div>
